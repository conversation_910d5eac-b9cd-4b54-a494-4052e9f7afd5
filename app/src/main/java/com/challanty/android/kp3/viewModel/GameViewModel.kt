package com.challanty.android.kp3.viewModel

import androidx.compose.ui.unit.IntSize
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import com.challanty.android.kp3.viewModel.helper.GameModelHelper
import com.challanty.android.kp3.viewModel.helper.GameStateHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class GameViewModel @Inject constructor(
    private val repository: Repository,
    private val processingStateManager: ProcessingStateManager,
    private val gameStateHelper: GameStateHelper,
    val gameModelHelper: GameModelHelper,
) : ViewModel() {

    init {
        viewModelScope.launch {
            waitForSavedInit()
            println("GameViewModel: waitForSavedInit RETURNED")
            processingStateManager.endProcessing(ProcessingType.APP)
            println("GameViewModel: App initialization complete")

            repository.settingsStateFlow.collect { settings ->
                println("GameViewModel: settings changed to ${gameModelHelper.displaySettings(settings)}")
                if (settings.version != 0) {
                    onGameSettingsChanged()
                }
            }
        }
    }

    private var isFirstGame = true
    private var currentGamingAreaSize = IntSize.Zero
    private val selections = mutableSetOf<TileModel>()

    // Expose the game state for use in the UI
    fun getGameState(): StateFlow<GameState> = gameStateHelper.gameState

    fun onGameScreenSizeChange(newSize: IntSize) {
        // Build new game resources if needed

        // Ignore the zero size returned at startup and any redundant changes.
        if (newSize == IntSize.Zero || newSize == currentGamingAreaSize) {
            println("GameViewModel: onGameScreenSizeChange ignoring $newSize")
            return
        }

        println("GameViewModel: onGameScreenSizeChange setting currentGamingAreaSize to $newSize")
        currentGamingAreaSize = newSize

        handleGameChange(calcDimensions = true)
    }

    fun onGameSettingsChanged() {

        if (gameModelHelper.isNewGameRequired()) {
            println("GameViewModel: gameSettingsChanged - invoking handleGameChange(): new game required.")
            handleGameChange(
                calcDimensions = true,
                startNewGame = true
            )
        } else {
            println("GameViewModel: gameSettingsChanged - invoking handleGameChange(): new game not required")
            handleGameChange(calcDimensions = true)
        }
    }

    fun onNewGameClick() {
        handleGameChange(startNewGame = true)
    }

    fun onTileClick(tile: TileModel) {
        if (processingStateManager.isProcessing()) return

        if (selections.contains(tile)) {
            selections.remove(tile)
        } else {
            selections.add(tile)
        }

        tile.toggleSelection()

        if (selections.size == 2) {
            handleTileSwap(selections.first(), selections.last())
        }
    }

    fun onTileDoubleClick(tile: TileModel) {
        if (processingStateManager.isProcessing()) return

        if (selections.contains(tile)) {
            selections.remove(tile)
            tile.toggleSelection()
        }

        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            gameStateHelper.handleUITileRotation(tile)

            if (gameModelHelper.handlePuzzleTileRotation(tile)) {
                gameStateHelper.toggleSolvedBoard(true)
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    private fun handleGameChange(
        startNewGame: Boolean = false,
        calcDimensions: Boolean = false,
    ) {

        if (currentGamingAreaSize == IntSize.Zero) {
            println("GameViewModel: handleGameChange called before we can handle it. currentGamingAreaSize: $currentGamingAreaSize, startNewGame: $startNewGame calcDimensions: $calcDimensions")
            return
        }
        println("GameViewModel: handleGameChange called with startNewGame: $startNewGame, calcDimensions: $calcDimensions")

        // Block input and show progress indicator while handling game change
        // Note: The game setup is so fast that the progress indicator hardly gets started
        processingStateManager.startProcessing(ProcessingType.GAME)
        gameStateHelper.toggleProgress(true)

        viewModelScope.launch {

            if (calcDimensions) {
                gameModelHelper.calcGameDimensions(currentGamingAreaSize)
            }

            if (startNewGame) {
                gameStateHelper.showNewGame(gameModelHelper.buildNewGame())
            } else {
                if (isFirstGame) {
                    println("GameViewModel: handleGameChange - first game")
                    isFirstGame = false
                    gameStateHelper.showStartupGame(gameModelHelper.buildExistingGame(currentGamingAreaSize))
                } else {
                    gameStateHelper.showExistingGame(gameModelHelper.buildExistingGame(currentGamingAreaSize))
                }
            }

            processingStateManager.endProcessing(ProcessingType.GAME)
        }

        return
    }

    private fun handleTileSwap(
        tile1: TileModel,
        tile2: TileModel
    ) {
        // Deselect the two selected tiles and swap their locations on the game board
        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            tile1.toggleSelection()
            tile2.toggleSelection()

            selections.clear()

            gameStateHelper.handleUITileSwap(tile1, tile2)

            if (gameModelHelper.handlePuzzleTileSwap(tile1, tile2)) {
                gameStateHelper.toggleSolvedBoard(true)
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    private suspend fun waitForSavedInit() {
        println("GameViewModel: waitForSavedInit - waiting for saved state to be initialized")
        repository.savedStateFlow.collect { saved ->
            println("GameViewModel: waitForSavedInit - saved state changed to $saved")
            if (!saved.board.isEmpty) {
                println("GameViewModel: waitForSavedInit - saved state is initialized...returning")
                return@collect
            }
        }
        println("GameViewModel: waitForSavedInit - returning")
    }
}