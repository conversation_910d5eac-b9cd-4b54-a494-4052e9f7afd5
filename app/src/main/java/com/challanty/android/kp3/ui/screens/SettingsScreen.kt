package com.challanty.android.kp3.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.challanty.android.kp3.R
import com.challanty.android.kp3.navigation.NavRoutes
import com.challanty.android.kp3.ui.components.DropdownSelector
import com.challanty.android.kp3.viewModel.SettingsViewModel

/**
 * Settings screen.
 * This screen allows the user to configure game settings.
 *
 * @param onNavigate Callback for when a navigation item is clicked.
 * @param viewModel The ViewModel for this screen.
 * @param onBackPressed Callback for when the back button is pressed.
 */
@Composable
fun SettingsScreen(
    onNavigate: (String) -> Unit,
    viewModel: SettingsViewModel = hiltViewModel(),
    onBackPressed: () -> Unit = {},
) {
    // Handle back button press to exit the app
    BackHandler {
        onBackPressed()
    }

    // Apply changes when leaving the screen
    DisposableEffect(Unit) {
        onDispose {
            println("SettingsScreen: DisposableEffect starting; viewModel.hasPendingChanges.value = ${viewModel.hasPendingChanges.value}")
            if (viewModel.hasPendingChanges.value) {
                println("SettingsScreen: DisposableEffect calling applyPendingChanges()")
                viewModel.applyPendingChanges()
            }
            println("SettingsScreen: DisposableEffect done")
        }
    }

    // Get valid values for tile dimensions based on current board dimension settings
    val validTileRowValues by viewModel.settingsValidTileRowValues.collectAsState()
    val validTileColumnValues by viewModel.settingsValidTileColumnValues.collectAsState()

    // Get current values as known by the Settings ViewModel (may not reflect the repository yet)
    val matrixRows by viewModel.settingsBoardRows.collectAsState()
    val matrixColumns by viewModel.settingBoardCols.collectAsState()
    val tileRows by viewModel.settingsTileRows.collectAsState()
    val tileColumns by viewModel.settingsTileColumns.collectAsState()
    // TODO add lock percent to screen
    val lockPercent by viewModel.settingsLockPercent.collectAsState()
    val tilesRotatable by viewModel.settingsTilesRotatable.collectAsState()
    val canRotateTiles by viewModel.settingsCanRotateTiles.collectAsState()
    val animate by viewModel.settingsAnimate.collectAsState()
    val animateRotation by viewModel.settingsAnimateRotation.collectAsState()
    val animateSwap by viewModel.settingsAnimateSwap.collectAsState()

    BaseScreen(
        title = stringResource(R.string.screen_title_settings),
        currentRoute = NavRoutes.Settings.route,
        onNavigate = onNavigate
    ) {
        // Use a scrollable column for the content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Add padding inside the scrollable content
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
            Text(
                text = stringResource(R.string.settings_title),
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 24.dp)
            )

            // Game Matrix Settings Card
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.settings_matrix_dimensions),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // Board Rows Dropdown
                    DropdownSelector(
                        label = stringResource(R.string.settings_matrix_rows, matrixRows),
                        selectedValue = matrixRows,
                        options = (1..6).toList(),
                        onValueSelected = { newValue ->
                            viewModel.setPendingBoardRows(newValue)
                        },
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Matrix Columns Dropdown
                    DropdownSelector(
                        label = stringResource(R.string.settings_matrix_columns, matrixColumns),
                        selectedValue = matrixColumns,
                        options = (1..6).toList(),
                        onValueSelected = { newValue ->
                            viewModel.setPendingBoardCols(newValue)
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Tile Settings Card
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.settings_tile_dimensions),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // Tile Rows Dropdown
                    DropdownSelector(
                        label = stringResource(R.string.settings_tile_rows, tileRows),
                        selectedValue = tileRows,
                        options = validTileRowValues,
                        onValueSelected = { newValue ->
                            viewModel.setPendingTileRows(newValue)
                        },
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Tile Columns Dropdown
                    DropdownSelector(
                        label = stringResource(R.string.settings_tile_columns, tileColumns),
                        selectedValue = tileColumns,
                        options = validTileColumnValues,
                        onValueSelected = { newValue ->
                            viewModel.setPendingTileColumns(newValue)
                        },
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Constraint explanation
                    if (matrixRows % 2 == 1 || matrixColumns % 2 == 1) {
                        Text(
                            text = stringResource(R.string.settings_constraint_explanation),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.secondary,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Rotatable Tiles Switch
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.settings_rotatable_tiles),
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        Switch(
                            checked = tilesRotatable,
                            onCheckedChange = { viewModel.setPendingTilesRotatable(it) },
                            enabled = canRotateTiles
                        )
                    }

                    // Explanation for rotatable tiles
                    if (!canRotateTiles) {
                        Text(
                            text = stringResource(R.string.settings_rotatable_constraint),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.secondary,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Animation Settings Card
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.settings_animation),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // Animation Switch
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.settings_animate),
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        Switch(
                            checked = animate,
                            onCheckedChange = { viewModel.setPendingAnimate(it) }
                        )
                    }

                    // Only show these options if animations are enabled
                    if (animate) {
                        // Animate Rotation Switch
                        Row(
                            modifier = Modifier.fillMaxWidth().padding(start = 16.dp, top = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.settings_animate_rotation),
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f)
                            )
                            Switch(
                                checked = animateRotation,
                                onCheckedChange = { viewModel.setPendingAnimateRotation(it) },
                                enabled = animate
                            )
                        }

                        // Animate Swap Switch
                        Row(
                            modifier = Modifier.fillMaxWidth().padding(start = 16.dp, top = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.settings_animate_swap),
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f)
                            )
                            Switch(
                                checked = animateSwap,
                                onCheckedChange = { viewModel.setPendingAnimateSwap(it) },
                                enabled = animate
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Debug Card
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Developer Options",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // Debug screen has been removed
                    // Uncomment if you want to add it back
                    /*
                    androidx.compose.material3.Button(
                        onClick = { onNavigate("debug") },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("View Cache Performance")
                    }
                    */
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Description text
            Text(
                text = stringResource(R.string.settings_description),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )

            // Add some space at the bottom for better appearance
            Spacer(modifier = Modifier.height(24.dp))
        }
        }
    }
}
