package com.challanty.android.kp3.ui.screens

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.challanty.android.kp3.R
import com.challanty.android.kp3.navigation.NavRoutes
import com.challanty.android.kp3.ui.components.GameBoard
import com.challanty.android.kp3.ui.components.GamingProgressIndicator
import com.challanty.android.kp3.ui.components.SolvedGameBoard
import com.challanty.android.kp3.viewModel.GameViewModel

/**
 * Game screen with custom layout for the game matrix.
 *
 * @param onNavigate Callback for when a navigation item is clicked.
 * @param gameViewModel The GameViewModel for this screen
 * @param onBackPressed Callback for when the back button is pressed
 */

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun GameScreen(
    onNavigate: (String) -> Unit,
    gameViewModel: GameViewModel = hiltViewModel(),
    onBackPressed: () -> Unit = {},
) {
    println("GameScreen: Composition starting")
    // Handle back button press to exit the app
    BackHandler { onBackPressed() }

    val gameState by gameViewModel.getGameState().collectAsState()

    BaseScreen(
        title = stringResource(R.string.screen_title_game),
        currentRoute = NavRoutes.Game.route,
        onNavigate = onNavigate,
        actions = {
            // New Game button in the title bar
            IconButton(
                onClick = gameViewModel::onNewGameClick
            ) {
                Icon(
                    // TODO() isn't there an icon for this?
                    painter = painterResource(id = R.drawable.ic_add),
                    contentDescription = stringResource(R.string.game_new_game)
                )
            }
        }
    ) {
        // Note: Although the Column has only one child, putting the screen
        // work area in a column allows it to be centered on the screen as desired.
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(color = Color.Transparent),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Transparent)
                    .onSizeChanged { size ->
                        gameViewModel.onGameScreenSizeChange(size)
                    },
                contentAlignment = Alignment.Center
            ) {
                println("GameScreen: Box content composition")
                if (gameState.showProgress) {
                    println("GameScreen: Showing progress indicator")
                    GamingProgressIndicator()
                }

                if (gameState.showGameBoard) {
                    println("GameScreen: Showing game board")
                    GameBoard(
                        boardPxSize = gameState.boardPxSize,
                        tilePxSize = gameState.tilePxSize,
                        tileModels = gameState.tileModels,
                        onTileClick = gameViewModel::onTileClick,
                        onTileDoubleClick = gameViewModel::onTileDoubleClick
                    )
                }

                if (gameState.showSolvedBoard) {
                    println("GameScreen: Showing solved board")
                    SolvedGameBoard(
                        solvedImage = gameState.solvedImage!!,
                        tintColor = gameState.solvedTintTargetColor,
                        backgroundColor = gameState.solvedBGTargetColor
                    )
                }
            }
        }
    }
}
