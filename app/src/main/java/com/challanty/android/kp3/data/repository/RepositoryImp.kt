package com.challanty.android.kp3.data.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.IOException
import androidx.datastore.dataStore
import com.challanty.android.kp3.data.Saved
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.util.Constants
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.google.protobuf.ByteString
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

private val Context.settingsDataStore: DataStore<Settings> by dataStore(
    fileName = "settings.pb",
    serializer = SettingsSerializer
)

private val Context.savedDataStore: DataStore<Saved> by dataStore(
    fileName = "saved.pb",
    serializer = SavedSerializer
)

@Singleton
class RepositoryImp @Inject constructor(
    @ApplicationContext private val context: Context
) : Repository {

    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    init {
        repositoryScope.launch {
            initRepository()
        }
    }

    private suspend fun initRepository() {
        try {
            if (settingsDataStore.data.first().version == 0) {
                initSettings()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            if (savedDataStore.data.first().board.isEmpty) {
                initSaved()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

// == Settings DataStore Section ==

    private val settingsDataStore = context.settingsDataStore

    // Hold user changes to settings until the user is done making change
    private var pendingSettings: Settings = Settings.getDefaultInstance()

    override val settingsFlow: Flow<Settings> = settingsDataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(Settings.getDefaultInstance())
            } else {
                throw exception
            }
        }

    override val settingsStateFlow: StateFlow<Settings> = settingsDataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(Settings.getDefaultInstance())
            } else {
                throw exception
            }
        }
        .stateIn(
            scope = repositoryScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = Settings.getDefaultInstance()
        )

    override fun pendingSetBoardRows(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setBoardRows(value).build()
    }

    override fun pendingSetBoardCols(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setBoardCols(value).build()
    }

    override fun pendingSetTileRows(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setTileRows(value).build()
    }

    override fun pendingSetTileCols(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setTileCols(value).build()
    }

    override fun pendingSetTilesRotatable(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setTilesRotatable(value).build()
    }

    override fun pendingSetAnimate(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setAnimate(value).build()
    }

    override fun pendingSetAnimateRotation(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setAnimateRotation(value).build()
    }

    override fun pendingSetAnimateSwap(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setAnimateSwap(value).build()
    }

    override fun pendingSetLockPercent(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setLockPercent(value).build()
    }

    override suspend fun onPendingFinalized() {
        println("RepositoryImp: onPendingFinalized(): Setting settings to $pendingSettings")
        settingsDataStore.updateData { pendingSettings }
    }

    override suspend fun initSettings() {
        println("RepositoryImp: initSettings()")
        try {
            settingsDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setVersion(Constants.VERSION)
                    .setBoardRows(Constants.DEFAULT_BOARD_ROWS)
                    .setBoardCols(Constants.DEFAULT_BOARD_COLS)
                    .setTileRows(Constants.DEFAULT_TILE_ROWS)
                    .setTileCols(Constants.DEFAULT_TILE_COLS)
                    .setLockPercent(Constants.DEFAULT_LOCK_PERCENT)
                    .setTilesRotatable(Constants.DEFAULT_TILES_ROTATABLE)
                    .setAnimate(Constants.DEFAULT_ANIMATE)
                    .setAnimateRotation(Constants.DEFAULT_ANIMATE_ROTATION)
                    .setAnimateSwap(Constants.DEFAULT_ANIMATE_SWAP)
                    .build()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

// == Saved DataStore Section ==

    private val savedDataStore = context.savedDataStore

    override val savedStateFlow: StateFlow<Saved> = savedDataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(Saved.getDefaultInstance())
            } else {
                throw exception
            }
        }
        .stateIn(
            scope = repositoryScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = Saved.getDefaultInstance()
        )

    override suspend fun setBoard(value: ByteString) {
        println("RepositoryImp: setBoard(): Setting board to $value")
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setBoard(value)
                    .build()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun setSolution(value: ByteString) {
        println("RepositoryImp: setSolution(): Setting solution to $value")
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setSolution(value)
                    .build()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun setLockedTiles(value: ByteString) {
        println("RepositoryImp: setLockedTiles(): Setting locked tiles to $value")
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setLockedTiles(value)
                    .build()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun initSaved() {
        println("RepositoryImp: initSaved()")
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setBoard(twoDintArray2ByteString(Constants.DEFAULT_BOARD))
                    .setSolution(twoDintArray2ByteString(Constants.DEFAULT_SOLUTION))
                    .setLockedTiles(twoDintArray2ByteString(Constants.DEFAULT_TILE_LOCKS))
                    .build()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }
}
