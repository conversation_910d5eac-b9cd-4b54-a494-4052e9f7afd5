package com.challanty.android.kp3

import android.app.Application
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class KP3Application : Application() {

    @Inject
    lateinit var processingStateManager: ProcessingStateManager

    @Inject
    lateinit var repository: Repository

    override fun onCreate() {
        super.onCreate()

        // Block input until the GameViewModel is told the repository is initialized.
        println("KP3Application: Starting APP processing")
        processingStateManager.startProcessing(ProcessingType.APP)
    }
}
