package com.challanty.android.kp3.data.repository

import com.challanty.android.kp3.data.Saved
import com.challanty.android.kp3.data.Settings
import com.google.protobuf.ByteString
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface Repository {
    val settingsFlow: Flow<Settings>
    val settingsStateFlow: StateFlow<Settings>
    val savedStateFlow: StateFlow<Saved>
    fun pendingSetBoardRows(value: Int)
    fun pendingSetBoardCols(value: Int)
    fun pendingSetTileRows(value: Int)
    fun pendingSetTileCols(value: Int)
    fun pendingSetTilesRotatable(value: Boolean)
    fun pendingSetLockPercent(value: Int)
    fun pendingSetAnimate(value: Boolean)
    fun pendingSetAnimateRotation(value: Boolean)
    fun pendingSetAnimateSwap(value: Boolean)
    suspend fun onPendingFinalized()
    suspend fun initSettings()
    suspend fun initSaved()
    suspend fun setBoard(value: ByteString)
    suspend fun setSolution(value: ByteString)
    suspend fun setLockedTiles(value: ByteString)
}