package com.challanty.android.kp3.viewModel.helper

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.pictureUnits.original.CelticPaths
import com.challanty.android.kp3.pictureUnits.original.CelticPictureUnitFactory
import com.challanty.android.kp3.puzzle.Puzzle
import com.challanty.android.kp3.puzzle.PuzzleService
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.util.Constants
import com.challanty.android.kp3.util.byteString2TwoDIntArray
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.challanty.android.kp3.viewModel.TileModel
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.min
import kotlin.random.Random

/**
 * Helper class for implementing GameViewModel business logic.
 */
@ViewModelScoped
class GameModelHelper @Inject constructor(
    private val puzzleService: PuzzleService,
    private val repository: Repository,
    coroutineScope: CoroutineScope
) {

    private var settings = Settings.getDefaultInstance()
    private val savedState = repository.savedStateFlow

    init {
        coroutineScope.launch {
            repository.settingsStateFlow.collect { it ->
                // Assigning settings uninitialized values is OK since that's how
                // the settings variable is initialized anyway.
                println("GameModelHelper: settings changed to ${displaySettings(it)}")
                settings = it
            }
        }
    }

    // TODO get rid of this
    private fun displaySettings(settings: Settings): String {
        return "version = ${settings.version}, board ${settings.boardRows}/${settings.boardCols}, tile ${settings.tileRows}/${settings.tileCols} lockPercent = ${settings.lockPercent}, tilesRotatable/animate/swap/rotation = ${settings.tilesRotatable}/${settings.animate}/${settings.animateRotation}/${settings.animateSwap}"
    }

    val ribbonPaint = Paint().apply {
        color = Color.Cyan // TODO: Make this a theme color
        style = PaintingStyle.Fill
        isAntiAlias = true
    }

    // Outline paint is recreated with each change to picUnitScale
    var outlinePaint: Paint = Paint()  // placeholder value

    private var picUnitSize: Float = 0f
    private var pictUnitScale: Float = 0f
    private var picUnitRows: Int = 0
    private var picUnitCols: Int = 0

    // Note: The puzzle must be initialized to an invalid
    // state so the existing puzzle at app startup can be detected
    // as the startup puzzle.
    private var puzzle = Puzzle()

    suspend fun buildNewGame(): GameState {

        println("GameModelHelper: buildNewGame() invoked")
        puzzle = puzzleService.generatePuzzle(
            rows = picUnitRows,
            cols = picUnitCols,
            tileRows = settings.tileRows,
            tileCols = settings.tileCols,
            seed = Random.nextLong(),
            isFlipOU = Random.nextBoolean()
        )

        // The unscrambled puzzle is the solution
        repository.setSolution(twoDintArray2ByteString(puzzle.picUnitMatrix))

        do {
            puzzleService.scramblePuzzle(
                puzzle = puzzle,
                doRotations = settings.tilesRotatable,
            )
        } while (puzzleService.isPuzzleSolved(puzzle))

        // The scrambled puzzle is the board
        repository.setBoard(twoDintArray2ByteString(puzzle.picUnitMatrix))

        return createGameResources(puzzle)
    }

    fun buildExistingGame(gamingAreaSize: IntSize): GameState {
        // An existing game is always fetched from the Saved datastore

        if (picUnitSize == 0f) {
            println("GameModelHelper: buildExistingGame() invoked BEFORE game dimensions are calculated")
            calcGameDimensions(gamingAreaSize)
        }
        else {
            println("GameModelHelper: buildExistingGame() invoked AFTER game dimensions are calculated")
        }

        puzzle = Puzzle(
            picUnitMatrix = byteString2TwoDIntArray(
                byteString = savedState.value.board,
                rows = picUnitRows,
                cols = picUnitCols
            ),
            tileRows = settings.tileRows,
            tileCols = settings.tileCols
        )

        return createGameResources(puzzle)
    }

    suspend fun handlePuzzleTileSwap(
        tile1: TileModel,
        tile2: TileModel
    ): Boolean {
        puzzleService.swapTiles(
            puzzle = puzzle,
            tile1 = tile1,
            tile2 = tile2,
        )
        repository.setBoard(twoDintArray2ByteString(puzzle.picUnitMatrix))

        return puzzleService.isPuzzleSolved(puzzle)
    }

    suspend fun handlePuzzleTileRotation(tile: TileModel): Boolean {
        puzzleService.rotateTile(
            puzzle = puzzle,
            tile = tile
        )
        repository.setBoard(twoDintArray2ByteString(puzzle.picUnitMatrix))

        return puzzleService.isPuzzleSolved(puzzle)
    }

    fun calcGameDimensions(gamingAreaSize: IntSize) {
        // gameAreaSize is guaranteed to be non-zero and settings is guaranteed to be initialized.
        println("GameModelHelper: calcGameDimensions(gamingAreaSize = $gamingAreaSize)")

        picUnitRows = settings.boardRows * settings.tileRows
        picUnitCols = settings.boardCols * settings.tileCols

        // Max possible picture unit pixel size if it could be rectangular
        val maxPicUnitHeight = gamingAreaSize.height / picUnitRows
        val maxPicUnitWidth = gamingAreaSize.width / picUnitCols

        // Use the smaller width/height size to make the largest possible
        // square picture unit that fits within the gaming area
        picUnitSize = min(maxPicUnitWidth, maxPicUnitHeight).toFloat()

        pictUnitScale = picUnitSize / Constants.PIC_UNIT_BOX_SIZE

        generateOutlinePaint()
    }

    fun isNewGameRequired(): Boolean {
        return if (puzzle.tileRows == 0) { // Must be startup
            false
        } else {
            settings.boardRows != puzzle.boardRows ||
                    settings.boardCols != puzzle.boardCols ||
                    settings.tileRows != puzzle.tileRows ||
                    settings.tileCols != puzzle.tileCols
        }
    }

    fun isAnimateSwap(): Boolean {
        return settings.animate && settings.animateSwap
    }

    fun isAnimateRotation(): Boolean {
        return settings.animate && settings.animateRotation
    }

    private fun createGameResources(puzzle: Puzzle): GameState {

        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        println("createGameResources: boardRows = $boardRows, boardCols = $boardCols, tileRows = $tileRows, tileCols = $tileCols")
        val tilePxSize = IntSize(
            width = (picUnitSize * tileCols).toInt(),
            height = (picUnitSize * tileRows).toInt()
        )

        val boardPxSize = IntSize(
            width = (picUnitSize * picUnitCols).toInt(),
            height = (picUnitSize * picUnitRows).toInt()
        )

        outlinePaint = Paint().apply {
            this.color = Color.Black
            this.style = PaintingStyle.Stroke
            this.strokeWidth = 0.1f * pictUnitScale * picUnitSize
            this.strokeCap = StrokeCap.Square
            this.isAntiAlias = true
        }

        val winBitmap = ImageBitmap(
            width = boardPxSize.width,
            height = boardPxSize.height
        )
        val winCanvas = Canvas(winBitmap)

        // AI says the conventional way to locate a graphic is by its center and radius.
        val drawingRadius = picUnitSize / 2
        val drawingCenter = Offset(drawingRadius, drawingRadius)

        var tileID = 0
        val tileModelList = mutableListOf<TileModel>()

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {

                val tileBitmap = ImageBitmap(
                    width = tilePxSize.width,
                    height = tilePxSize.height
                )
                val tileCanvas = Canvas(tileBitmap)

                for (tileRow in 0 until tileRows) {
                    val puzzleRow = boardRow * tileRows + tileRow

                    for (tileCol in 0 until tileCols) {
                        val puzzleCol = boardCol * tileCols + tileCol

                        val pictureUnit = puzzle.getPictureUnitAt(
                            row = puzzleRow,
                            col = puzzleCol
                        )

                        val pictureUnitStrategy =
                            CelticPictureUnitFactory.createPictureUnit(pictureUnit)
                        val paths = pictureUnitStrategy.createPaths(drawingCenter, drawingRadius)

                        drawPictureUnit(
                            canvas = tileCanvas,
                            paths = paths,
                            x = tileCol * picUnitSize,
                            y = tileRow * picUnitSize,
                            ribbonPaint = ribbonPaint,
                            outlinePaint = outlinePaint
                        )
                        drawPictureUnit(
                            canvas = winCanvas,
                            paths = paths,
                            x = puzzleCol * picUnitSize,
                            y = puzzleRow * picUnitSize,
                            ribbonPaint = ribbonPaint,
                            outlinePaint = outlinePaint
                        )
                    }
                }

                tileModelList.add(
                    TileModel(
                        id = tileID,
                        bitmap = tileBitmap,
                        boardPosition = IntOffset(boardRow, boardCol),
                        initIntOffset = IntOffset.Zero,
                        initIntOffsetDuration = 0,
                        initQuarterTurnCnt = 0,
                        initRotationDuration = 0,
                        initIsLocked = false,
                        initIsSelected = false,
                    )
                )
                tileID++
            }
        }

        val (color1, color2) = generateWinColors()

        return GameState(
            showProgress = false,
            showGameBoard = true,
            showSolvedBoard = false,
            boardPxSize = boardPxSize,
            tilePxSize = tilePxSize,
            tileModels = tileModelList.toList(),
            solvedImage = winBitmap,
            solvedTintTargetColor = color1,
            solvedBGTargetColor = color2
        )
    }

    private fun drawPictureUnit(
        canvas: Canvas,
        paths: CelticPaths,
        x: Float,
        y: Float,
        ribbonPaint: Paint,
        outlinePaint: Paint
    ) {
        with(canvas) {
            save()
            translate(x, y)

            drawPath(paths.getRibbonPath(), ribbonPaint)
            drawPath(paths.getOutlinePath(), outlinePaint)

            restore()
        }
    }

    private fun generateWinColors(): Pair<Color, Color> {
        val r = Random.nextInt(0, 256)
        val g = Random.nextInt(0, 256)
        val b = Random.nextInt(0, 256)

        val color1 = Color(red = 255 - r, green = 255 - g, blue = 255 - b)
        val color2 = Color(red = r, green = g, blue = b)

        return Pair(color1, color2)
    }

    private fun generateOutlinePaint(): Paint {
        return Paint().apply {
            color = Color.Black // TODO: Make this a theme color
            style = PaintingStyle.Stroke
            strokeWidth = 10f * pictUnitScale
            strokeCap = StrokeCap.Square
            isAntiAlias = true
        }
    }

    // TODO move this to a test utility
    private fun showPicUnitMatrix(matrix: Array<IntArray>): String {
        // Convert the 2D array to a string representation
        // Convert the IDs 0-25 to their corresponding picture unit names a-z
        // without modifying the original array
        return matrix.joinToString("\n") { row ->
            row.joinToString("") { value ->
                (value % 26 + 97).toChar().toString()
            }
        }
    }
}
